# 商旅巡游录 - 20个词条完整测试指南

## 🎯 测试目标

本指南将帮助你系统性地测试所有20个词条，确保每个词条的效果都能正常工作。

## 📋 20个词条完整列表

### 正向词条（8个）
1. **bountiful_harvest** - 丰收之日：采集额外获得1个相同物品       测试结果：正常
2. **swift_feet** - 疾风步伐：移动速度大幅提升                     测试结果：正常
3. **iron_stomach** - 铁胃：饥饿值消耗减半                        测试结果：正常
4. **clear_mind** - 澄澈心智：理智值不会自然下降                   测试结果：正常
5. **lucky_strike** - 幸运打击：攻击有概率造成双倍伤害             测试结果：正常
6. **master_crafter** - 工匠大师：制作物品消耗材料减半             测试结果：正常
7. **night_vision** - 夜视：夜晚视野如同白昼                      测试结果：正常
8. **beast_friend** - 兽语者：中性生物不会主动攻击                测试结果：正常

### 负向词条（8个）
9. **fragile_tools** - 脆弱工具：工具耐久消耗翻倍                 测试结果：正常
10. **restless_night** - 不眠之夜：夜晚理智快速流失                测试结果：正常
11. **clumsy_hands** - 笨拙之手：有概率掉落手持物品                测试结果：正常
12. **heavy_burden** - 负重前行：物品栏满时移动缓慢                测试结果：正常
13. **monster_magnet** - 怪物磁铁：敌对生物更容易发现你            测试结果：正常
14. **wet_weather** - 阴雨连绵：持续下雨                          测试结果：正常
15. **cold_snap** - 寒流来袭：温度持续下降（火炉旁不会下降）        测试结果：正常
16. **food_spoilage** - 腐败加速：食物腐烂速度翻倍                 测试结果：正常

### 中性/事件词条（4个）
17. **glass_cannon** - 玻璃大炮：攻击力翻倍，但生命值减半          测试结果：正常
18. **night_owl** - 夜猫子：夜晚获得各种加成，白天虚弱             测试结果：正常
19. **berserker** - 狂战士：生命值越低攻击力越高                  测试结果：正常
20. **merchant_visit** - 商人造访：今日将有特殊商队到访

## 🛠️ 测试准备

### 1. 启动测试环境
```lua
-- 开启调试模式
c_godmode()
c_supergodmode()

-- 准备测试物品
c_give("backpack", 1)
c_give("axe", 5)
c_give("pickaxe", 5)
c_give("shovel", 5)
c_give("spear", 5)
c_give("berries", 20)
c_give("log", 40)
c_give("rocks", 40)

-- 加载测试助手
dofile("mods/thinking/词条测试助手.lua")
```

### 2. 基础命令

**聊天命令（直接在聊天框输入）：**
- `cmutators` - 查看当前词条
- `cstatus` - 检查系统状态

**控制台命令（按`键打开控制台）：**
- `c_rerolll()` - 重掷词条（无限制）
- `c_mutators()` - 查看当前词条
- `c_findmutator("词条名")` - 自动寻找特定词条

## 🔍 详细测试方法

### 正向词条测试

#### 1. 丰收之日 (bountiful_harvest)
**测试方法：**
```lua
-- 方法1：使用控制台命令寻找词条
c_findmutator("丰收")

-- 方法2：手动重掷直到找到
c_mutators()  -- 查看当前词条
c_rerolll()   -- 如果没有目标词条就重掷

-- 测试采集
c_spawn("sapling") -- 生成小树苗
-- 采集小树苗，观察是否获得额外物品
```
**预期效果：** 采集时获得双倍产出

#### 2. 疾风步伐 (swift_feet)
**测试方法：**
```lua
c_findmutator("疾风")
-- 观察角色移动速度
-- 对比有无词条时的移动速度
```
**预期效果：** 移动速度明显提升50%

#### 3. 铁胃 (iron_stomach)
**测试方法：**
```lua
FindMutator("铁胃")
-- 观察饥饿值下降速度
-- 等待一段时间查看饥饿条变化
```
**预期效果：** 饥饿值消耗减半

#### 4. 澄澈心智 (clear_mind)
**测试方法：**
```lua
FindMutator("澄澈")
-- 观察理智值变化
-- 在黑暗中等待，理智应该不会自然下降
```
**预期效果：** 理智值保持稳定

#### 5. 幸运打击 (lucky_strike)
**测试方法：**
```lua
FindMutator("幸运")
c_spawn("spider") -- 生成蜘蛛
-- 多次攻击，观察伤害数字
```
**预期效果：** 偶尔出现双倍伤害

#### 6. 工匠大师 (master_crafter)
**测试方法：**
```lua
FindMutator("工匠")
-- 制作物品，观察材料消耗
-- 例如制作斧头，查看消耗的材料数量
```
**预期效果：** 制作消耗材料减半

#### 7. 夜视 (night_vision)
**测试方法：**
```lua
FindMutator("夜视")
c_setstep("night") -- 切换到夜晚
-- 观察夜晚视野范围
```
**预期效果：** 夜晚视野如同白昼

#### 8. 兽语者 (beast_friend)
**测试方法：**
```lua
FindMutator("兽语")
c_spawn("pigman") -- 生成猪人
-- 接近猪人，观察其反应
```
**预期效果：** 中性生物不会主动攻击

### 负向词条测试

#### 9. 脆弱工具 (fragile_tools)
**测试方法：**
```lua
FindMutator("脆弱")
-- 使用工具砍树、挖矿
-- 观察工具耐久消耗速度
```
**预期效果：** 工具耐久消耗翻倍

#### 10. 不眠之夜 (restless_night)
**测试方法：**
```lua
FindMutator("不眠")
c_setstep("night")
-- 观察夜晚理智值下降速度
```
**预期效果：** 夜晚理智快速流失

#### 11. 笨拙之手 (clumsy_hands)
**测试方法：**
```lua
c_findmutator("笨拙")

-- 测试1：工具使用（砍树、挖矿）
c_give("axe", 1)
c_spawn("evergreen")
-- 手持斧头砍树，观察是否会掉落斧头

-- 测试2：攻击动作
c_give("spear", 1)
c_spawn("spider")
-- 手持长矛攻击蜘蛛，观察是否会掉落长矛

-- 测试3：采集动作
c_give("shovel", 1)
c_spawn("berrybush")
-- 手持铲子采集浆果，观察是否会掉落铲子
```
**预期效果：** 在使用工具、攻击、采集时都有10%概率掉落手持物品

#### 12. 负重前行 (heavy_burden)
**测试方法：**
```lua
c_findmutator("负重")
-- 装满背包（15个格子都有物品）
c_give("log", 1)      -- 给各种物品填满15个格子
c_give("rocks", 1)
c_give("twigs", 1)
-- ... 直到15个格子都满
-- 观察移动速度变化
```
**预期效果：** 物品栏满时（15/15格子）移动缓慢

#### 13. 怪物磁铁 (monster_magnet)
**测试方法：**
```lua
FindMutator("怪物")
c_spawn("spider")
-- 观察怪物的仇恨范围
```
**预期效果：** 敌对生物更容易发现玩家

#### 14. 阴雨连绵 (wet_weather)
**测试方法：**
```lua
FindMutator("阴雨")
-- 观察天气状态
-- 检查潮湿值变化
```
**预期效果：** 持续下雨，潮湿值上升

#### 15. 寒流来袭 (cold_snap)
**测试方法：**
```lua
FindMutator("寒流")
-- 观察温度计数值
-- 检查体温变化
```
**预期效果：** 温度持续下降

#### 16. 腐败加速 (food_spoilage)
**测试方法：**
```lua
FindMutator("腐败")
c_give("berries", 5)
-- 观察食物腐烂速度
```
**预期效果：** 食物腐烂速度翻倍

### 中性/事件词条测试

#### 17. 玻璃大炮 (glass_cannon)
**测试方法：**
```lua
FindMutator("玻璃")
-- 检查生命值上限
-- 测试攻击力
```
**预期效果：** 攻击力翻倍，生命值减半

#### 18. 夜猫子 (night_owl)
**测试方法：**
```lua
FindMutator("夜猫")
-- 白天和夜晚分别测试各项能力
-- 对比差异
```
**预期效果：** 夜晚有加成，白天虚弱

#### 19. 狂战士 (berserker)
**测试方法：**
```lua
FindMutator("狂战")
-- 满血时测试攻击力
-- 受伤到低血量后再测试
```
**预期效果：** 血量越低攻击力越高

#### 20. 商人造访 (merchant_visit)
**测试方法：**
```lua
FindMutator("商人")
-- 等待并观察是否有商队生成
```
**预期效果：** 特殊商队出现

## 📊 测试记录表

创建一个表格记录每个词条的测试结果：

| 词条ID | 词条名称 | 测试状态 | 效果是否正常 | 备注 |
|--------|----------|----------|--------------|------|
| bountiful_harvest | 丰收之日 | ⬜ 未测试 | ⬜ | |
| swift_feet | 疾风步伐 | ⬜ 未测试 | ⬜ | |
| ... | ... | ... | ... | ... |

## 🚀 快速测试流程

### 方法1：使用控制台命令（推荐）
1. **按 ` 键打开控制台**
2. **查看当前词条：** `c_mutators()`
3. **寻找目标词条：** `c_findmutator("词条名")`
4. **进行测试**
5. **记录结果**
6. **重掷词条：** `c_rerolll()`
7. **重复步骤2-6**

### 方法2：使用测试助手脚本
1. **加载测试助手：** `dofile("mods/thinking/词条测试助手.lua")`
2. **准备环境：** `PrepareTestEnvironment()`
3. **查看当前词条：** `ShowCurrentMutators()`
4. **寻找目标词条：** `FindMutator("词条名")`
5. **进行测试**
6. **记录结果**
7. **重掷词条：** `c_rerolll()`
8. **重复步骤3-7**

## ⚠️ 注意事项

1. **权限要求：** `c_rerolll()`命令需要管理员权限
2. **测试环境：** 建议在单人模式或私人服务器测试
3. **记录详细：** 详细记录每个词条的测试结果和发现的问题
4. **多次验证：** 重要词条建议多次测试确认
5. **系统重置：** 测试负向词条后建议重启游戏重置状态

## 📈 测试完成标准

- ✅ 所有20个词条都已测试
- ✅ 每个词条的效果都能正常触发
- ✅ 没有发现严重的游戏崩溃或错误
- ✅ 词条效果符合设计预期
- ✅ 多人游戏环境下测试通过

完成所有测试后，您就能确保词条系统的稳定性和趣味性了！
