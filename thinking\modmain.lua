local _G = GLOBAL
GLOBAL.setmetatable(env, { __index = function(_, k) return GLOBAL.rawget(GLOBAL, k) end })

-- Basic helpers
local function Announce(msg)
    if TheNet:GetIsServer() then
        TheNet:Announce("[商旅巡游录] " .. tostring(msg))
    end
end

local function ModSay(player, msg)
    if player and player.components and player.components.talker then
        player.components.talker:Say("[商旅巡游录] " .. tostring(msg))
    end
end

-- Load tuning first, then components
modimport("scripts/tuning.lua")

-- Prefabs
PrefabFiles = PrefabFiles or {}
local add = table.insert
add(PrefabFiles, "caravan_light")


-- Register components files (load them so they're available)
modimport("scripts/components/" .. "modworld_global" .. ".lua")
modimport("scripts/components/" .. "modplayer_boons" .. ".lua")
modimport("scripts/components/" .. "modplayer_rep" .. ".lua")
modimport("scripts/components/" .. "modplayer_reroll" .. ".lua")

-- Components paths
local WORLD_COMP = "modworld_global"
local PLAYER_BOONS = "modplayer_boons"
local PLAYER_REP = "modplayer_rep"

-- Attach world component early so its OnLoad runs properly
AddPrefabPostInit("world", function(inst)
    if inst.ismastersim then
        if not inst.components[WORLD_COMP] then
            inst:AddComponent(WORLD_COMP)
        end
    end
end)

-- Attach player components
AddPlayerPostInit(function(inst)
    if not TheWorld or not TheWorld.ismastersim then return end
    if not inst.components[PLAYER_BOONS] then
        inst:AddComponent(PLAYER_BOONS)
    end
    if not inst.components[PLAYER_REP] then
        inst:AddComponent(PLAYER_REP)
    end
    if not inst.components["modplayer_reroll"] then
        inst:AddComponent("modplayer_reroll")
    end

    -- 延迟应用词条效果，确保所有组件都已初始化
    inst:DoTaskInTime(1, function()
        if inst:IsValid() and not inst:HasTag("playerghost") then
            local MutatorEffects = require("systems/mutator_effects")
            if MutatorEffects then
                MutatorEffects.ApplyToPlayer(inst)
                print("[商旅巡游录] 已为玩家应用词条效果:", inst.userid or "unknown")
            end
        end
    end)

    -- 监听玩家重生事件，重新应用效果
    inst:ListenForEvent("ms_respawnedfromghost", function()
        inst:DoTaskInTime(0.5, function()
            if inst:IsValid() and not inst:HasTag("playerghost") then
                local MutatorEffects = require("systems/mutator_effects")
                if MutatorEffects then
                    MutatorEffects.ApplyToPlayer(inst)
                    print("[商旅巡游录] 玩家重生后重新应用词条效果:", inst.userid or "unknown")
                end

                -- 重新应用被动恩惠效果
                if inst.components.modplayer_boons then
                    inst.components.modplayer_boons:ReapplyAllEffects()
                    print("[商旅巡游录] 玩家重生后重新应用被动恩惠效果:", inst.userid or "unknown")
                end
            end
        end)
    end)

    -- 监听玩家变成幽灵事件，清除词条效果
    inst:ListenForEvent("ms_becameghost", function()
        local MutatorEffects = require("systems/mutator_effects")
        if MutatorEffects then
            MutatorEffects.RemoveFromPlayer(inst)
            print("[商旅巡游录] 玩家变成幽灵，清除词条效果:", inst.userid or "unknown")
        end
    end)

    -- 监听玩家加入事件，确保新加入的玩家也能获得词条效果
    inst:ListenForEvent("playeractivated", function()
        inst:DoTaskInTime(2, function()
            if inst:IsValid() and not inst:HasTag("playerghost") then
                local MutatorEffects = require("systems/mutator_effects")
                if MutatorEffects then
                    MutatorEffects.ApplyToPlayer(inst)
                    print("[商旅巡游录] 玩家激活后应用词条效果:", inst.userid or "unknown")
                end
            end
        end)
    end)
end)

-- Load commands after game initialization
AddSimPostInit(function()
    modimport("scripts/systems/commands.lua")
    if TheWorld and TheWorld.ismastersim then
        Announce("已加载：每日词条、合约、恩惠、阵营、商队、Boss进化（默认开启）。输入 chelp 查看指令或按 V 键打开UI")
    end
end)

-- UI System
local CaravanScreen = require("screens/caravanscreen")

-- Mutator effects system will be required where needed:
-- require("systems/mutator_effects")

-- Global variable to track UI state
local caravan_ui_open = false

-- Simple key handler for V key
local function ToggleCaravanUI()
    local player = ThePlayer
    if not player then return end

    if caravan_ui_open then
        -- Close UI
        TheFrontEnd:PopScreen()
        caravan_ui_open = false
    else
        -- Open UI
        local screen = CaravanScreen(player)
        TheFrontEnd:PushScreen(screen)
        caravan_ui_open = true

        -- Listen for screen close to update state
        screen.inst:ListenForEvent("onremove", function()
            caravan_ui_open = false
        end)
    end
end

-- Expose function globally for chat commands
rawset(_G, "ToggleCaravanUI", ToggleCaravanUI)

-- Register V key handler
AddSimPostInit(function()
    if TheInput then
        TheInput:AddKeyDownHandler(KEY_V, ToggleCaravanUI)
    end
end)

-- Expose helpers to systems
rawset(_G, "CARAVAN_MOD_ANNOUNCE", Announce)
rawset(_G, "CARAVAN_MOD_SAY", ModSay)

-- Components already loaded above

