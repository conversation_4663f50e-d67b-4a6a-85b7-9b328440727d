-- 测试修复后的代码
-- 这个脚本用于验证 DoTaskInTime 错误是否已修复

print("=== 商旅巡游录 修复测试 ===")

-- 检查世界组件是否正常工作
local function TestWorldComponent()
    if not TheWorld then
        print("✗ TheWorld 不存在")
        return false
    end
    
    if not TheWorld.components then
        print("✗ TheWorld.components 不存在")
        return false
    end
    
    local world_comp = TheWorld.components.modworld_global
    if not world_comp then
        print("✗ modworld_global 组件未找到")
        return false
    end
    
    print("✓ modworld_global 组件存在")
    
    -- 检查组件的基本属性
    if world_comp.mutators then
        print("✓ mutators 属性存在，当前数量:", #world_comp.mutators)
    else
        print("✗ mutators 属性不存在")
    end
    
    if world_comp.current_day ~= nil then
        print("✓ current_day 属性存在，当前值:", world_comp.current_day)
    else
        print("✗ current_day 属性不存在")
    end
    
    return true
end

-- 检查玩家组件是否正常工作
local function TestPlayerComponents()
    if not ThePlayer then
        print("✗ ThePlayer 不存在")
        return false
    end
    
    if not ThePlayer.components then
        print("✗ ThePlayer.components 不存在")
        return false
    end
    
    local boons_comp = ThePlayer.components.modplayer_boons
    if boons_comp then
        print("✓ modplayer_boons 组件存在")
    else
        print("✗ modplayer_boons 组件不存在")
    end
    
    local rep_comp = ThePlayer.components.modplayer_rep
    if rep_comp then
        print("✓ modplayer_rep 组件存在")
    else
        print("✗ modplayer_rep 组件不存在")
    end
    
    local reroll_comp = ThePlayer.components.modplayer_reroll
    if reroll_comp then
        print("✓ modplayer_reroll 组件存在")
    else
        print("✗ modplayer_reroll 组件不存在")
    end
    
    return true
end

-- 测试GLOBAL变量访问修复
local function TestGlobalAccess()
    print("\n=== 测试GLOBAL变量访问修复 ===")

    -- 测试modplayer_boons组件的GLOBAL访问
    local success, err = pcall(function()
        local ModPlayerBoons = require("components/modplayer_boons")
        print("✓ modplayer_boons.lua 加载成功 (GLOBAL访问正常)")
        return true
    end)

    if not success then
        print("✗ modplayer_boons.lua 加载失败:", err)
        return false
    end

    -- 测试其他系统文件
    success, err = pcall(function()
        local Commands = require("systems/commands")
        print("✓ commands.lua 加载成功 (GLOBAL访问正常)")
        return true
    end)

    if not success then
        print("✗ commands.lua 加载失败:", err)
        return false
    end

    print("✓ 所有GLOBAL变量访问测试通过")
    return true
end

-- 测试词条效果系统
local function TestMutatorEffects()
    local success, MutatorEffects = pcall(require, "systems/mutator_effects")
    if not success then
        print("✗ 无法加载 mutator_effects 系统:", MutatorEffects)
        return false
    end

    print("✓ mutator_effects 系统加载成功")

    if MutatorEffects.ApplyToPlayer then
        print("✓ ApplyToPlayer 方法存在")
    else
        print("✗ ApplyToPlayer 方法不存在")
    end

    if MutatorEffects.RemoveFromPlayer then
        print("✓ RemoveFromPlayer 方法存在")
    else
        print("✗ RemoveFromPlayer 方法不存在")
    end

    return true
end

-- 主测试函数
local function RunTests()
    print("开始测试...")

    local global_ok = TestGlobalAccess()
    local world_ok = TestWorldComponent()
    local player_ok = TestPlayerComponents()
    local effects_ok = TestMutatorEffects()

    if global_ok and world_ok and player_ok and effects_ok then
        print("\n🎉 所有测试通过！GLOBAL变量访问修复成功！")
        print("mod应该可以正常运行了")
    else
        print("\n❌ 部分测试失败，可能还有问题需要解决")
    end

    print("=== 测试完成 ===")
end

-- 延迟执行测试，确保所有组件都已加载
if TheWorld and ThePlayer then
    RunTests()
else
    -- 等待世界和玩家加载完成
    local function DelayedTest()
        if TheWorld and ThePlayer then
            RunTests()
        else
            print("等待世界和玩家加载...")
            if TheWorld then
                TheWorld:DoTaskInTime(2, DelayedTest)
            end
        end
    end
    
    DelayedTest()
end
