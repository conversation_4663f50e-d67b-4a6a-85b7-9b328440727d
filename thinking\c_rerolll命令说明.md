# c_rerolll() 命令说明

## 📋 命令概述

我已经成功添加了 `c_rerolll()` 命令，用于方便测试20个词条。这个命令绕过了每日重投限制，可以无限次重掷词条。

## 🔧 技术实现

### 命令定义位置
- **文件：** `thinking/scripts/systems/commands.lua`
- **实现方式：** 使用官方的 `AddUserCommand` API
- **命令名：** `rerolll`（在控制台中使用 `c_rerolll()`）

### 代码实现
```lua
-- 添加全局控制台命令 c_rerolll (用于测试)
local function c_rerolll()
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if world_comp then
        print("[商旅巡游录] c_rerolll: 强制重掷词条 (测试命令)")
        world_comp:RollMutators()
        world_comp:AnnounceMutators()
        print("[商旅巡游录] c_rerolll: 重掷完成")
        return "已强制重掷词条！(测试命令)"
    else
        print("[商旅巡游录] c_rerolll: 世界组件未初始化")
        return "世界组件未初始化"
    end
end

-- 将命令注册到全局环境，这样就可以在控制台使用 c_rerolll()
GLOBAL.rawset(GLOBAL, "c_rerolll", c_rerolll)
```

## 🎮 使用方法

### 基本用法
```lua
c_rerolll()  -- 直接在控制台输入（按`键打开控制台）
c_mutators() -- 查看当前词条
c_findmutator("丰收") -- 自动寻找包含"丰收"的词条
```

### 权限要求
- **无需特殊权限**
- 在单人游戏和多人游戏中都可以使用
- 仅用于测试目的

### 功能特点
1. **绕过限制：** 不受每日重投次数限制
2. **即时生效：** 立即重新生成词条并应用到所有玩家
3. **全服公告：** 自动向所有玩家公告新的词条
4. **测试专用：** 仅用于测试目的，不影响正常游戏平衡

## 📊 20个词条完整列表

### 正向词条（8个）
1. **bountiful_harvest** - 丰收之日：采集额外获得1个相同物品
2. **swift_feet** - 疾风步伐：移动速度大幅提升
3. **iron_stomach** - 铁胃：饥饿值消耗减半
4. **clear_mind** - 澄澈心智：理智值不会自然下降
5. **lucky_strike** - 幸运打击：攻击有概率造成双倍伤害
6. **master_crafter** - 工匠大师：制作物品消耗材料减半
7. **night_vision** - 夜视：夜晚视野如同白昼
8. **beast_friend** - 兽语者：中性生物不会主动攻击

### 负向词条（8个）
9. **fragile_tools** - 脆弱工具：工具耐久消耗翻倍
10. **restless_night** - 不眠之夜：夜晚理智快速流失
11. **clumsy_hands** - 笨拙之手：有概率掉落手持物品
12. **heavy_burden** - 负重前行：携带物品超过15个时移动缓慢
13. **monster_magnet** - 怪物磁铁：敌对生物更容易发现你
14. **wet_weather** - 阴雨连绵：持续下雨，潮湿值上升
15. **cold_snap** - 寒流来袭：温度持续下降
16. **food_spoilage** - 腐败加速：食物腐烂速度翻倍

### 中性/事件词条（4个）
17. **glass_cannon** - 玻璃大炮：攻击力翻倍，但生命值减半
18. **night_owl** - 夜猫子：夜晚获得各种加成，白天虚弱
19. **berserker** - 狂战士：生命值越低攻击力越高
20. **merchant_visit** - 商人造访：今日将有特殊商队到访

## 🧪 测试策略

### 快速测试流程
1. **启动游戏并加载mod**
2. **进入世界（单人或多人游戏）**
3. **按 ` 键打开控制台**
4. **查看当前词条：**
   ```lua
   c_mutators()
   ```
5. **寻找特定词条：**
   ```lua
   c_findmutator("丰收")  -- 自动重掷直到找到目标词条
   ```
6. **手动重掷：**
   ```lua
   c_rerolll()  -- 立即重掷获得新词条
   ```
7. **可选：使用测试助手脚本：**
   ```lua
   dofile("mods/thinking/词条测试助手.lua")
   ShowCurrentMutators()
   ```

### 系统性测试方法
1. **准备测试环境**
2. **逐个测试每种词条**
3. **记录测试结果**
4. **验证词条效果**

## 🔍 测试工具

### 已有的测试工具
1. **词条测试助手脚本** (`thinking/词条测试助手.lua`)
2. **20个词条完整测试指南** (`thinking/20个词条完整测试指南.md`)
3. **词条测试完整方案** (`thinking/词条测试完整方案.md`)

### 相关命令
**聊天命令（直接在聊天框输入）：**
- `cmutators` - 查看当前词条
- `cstatus` - 检查系统状态
- `creroll` - 正常重投（受每日限制）

**控制台命令（按`键打开控制台输入）：**
- `c_rerolll()` - 强制重投（测试用，无限制）
- `c_mutators()` - 查看当前词条（控制台版本）
- `c_findmutator("词条名")` - 自动寻找特定词条

## ⚠️ 注意事项

### 使用限制
1. **仅用于测试：** 这个命令是为了方便测试而设计的
2. **无权限限制：** 任何玩家都可以使用
3. **不影响平衡：** 不应在正常游戏中使用

### 安全考虑
1. **测试专用：** 仅用于测试词条效果
2. **建议环境：** 建议在测试环境或私人服务器使用
3. **备份存档：** 测试前建议备份存档

## 📈 测试目标

通过使用 `c_rerolll()` 命令，你可以：

1. **快速验证所有20个词条**
2. **确保每个词条效果正常工作**
3. **发现并修复潜在问题**
4. **验证词条系统的稳定性**
5. **确保多人游戏兼容性**

## 🎯 下一步

1. **开始系统性测试：** 使用提供的测试指南
2. **记录测试结果：** 详细记录每个词条的表现
3. **报告问题：** 如发现问题，请详细描述
4. **优化改进：** 根据测试结果进行必要的调整

现在你已经拥有了完整的测试工具，可以开始对20个词条进行全面测试了！
