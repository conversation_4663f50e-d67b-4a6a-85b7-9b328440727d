
[01:29:17]: [string "../mods/thinking/scripts/screens/caravanscr..."]:65: attempt to call field 'ScrollBox' (a nil value)
LUA ERROR stack traceback:
../mods/thinking/scripts/screens/caravanscreen.lua:65 in (field) _ctor (Lua) <8-95>
   self =
      callbacks = table: 00000000D207DC90
      inst = 129280 -  (valid:true)
      focus = false
      handlers = table: 00000000D207CD40
      children = table: 00000000D207CFC0
      info_area = info_area
      focus_flow_args = table: 00000000D207CB10
      focus_target = false
      current_tab = mutators
      root = root
      owner = 116902 - winona (valid:true)
      tabs = table: 00000000B90DCE90
      can_fade_alpha = true
      shown = true
      panel = NineSlice
      name = CaravanScreen
      enabled = true
      focus_flow = table: 00000000D207D740
      bg = BUTTON
      content = content
      is_screen = true
   owner = 116902 - winona (valid:true)
   tab_names = table: 00000000B90DCF80
scripts/class.lua:191 in (upvalue) CaravanScreen (Lua) <181-194>
   class_tbl = table: 000000007D0719D0
   arg = nil
   obj = CaravanScreen
../mods/thinking/modmain.lua:140 in (field) fn (Lua) <130-149>
   player = 116902 - winona (valid:true)
scripts/events.lua:46 in (method) HandleEvent (Lua) <42-49>
   self =
      events = table: 0000000016F22710
   event = 118
   arg = nil
   handlers = table: 00000000C4EE3680
   k = table: 00000000C4EE3630
   v = true
scripts/input.lua:189 in (method) OnRawKey (Lua) <186-193>
   self =
      hoverinst = 110635 - spidergland (valid:true)
      onkeyup = table: 0000000016F22080
      entitiesundermouse = table: 00000000D2072AC0
      onmousebutton = table: 0000000016F22800
      controllerid_cached = 0
      onkey = table: 0000000016F22300
      enabledebugtoggle = true
      ongesture = table: 0000000016F23CA0
      mouse_enabled = true
      ontextinput = table: 0000000016F23C50
      position = table: 0000000016F220D0
      onkeydown = table: 0000000016F22670
      oncontrol = table: 0000000016F223A0
   key = 118
   down = true
scripts/input.lua:765 in () ? (Lua) <764-766>
   key = 118
   is_up = true

[01:29:18]: [string "../mods/thinking/scripts/screens/caravanscr..."]:65: attempt to call field 'ScrollBox' (a nil value)
LUA ERROR stack traceback:
    ../mods/thinking/scripts/screens/caravanscreen.lua:65 in (field) _ctor (Lua) <8-95>
    scripts/class.lua:191 in (upvalue) CaravanScreen (Lua) <181-194>
    ../mods/thinking/modmain.lua:140 in (field) fn (Lua) <130-149>
    scripts/events.lua:46 in (method) HandleEvent (Lua) <42-49>
    scripts/input.lua:189 in (method) OnRawKey (Lua) <186-193>
    scripts/input.lua:765 in () ? (Lua) <764-766>
	
[01:29:18]: [workshop-2189004162 (Insight)]:	A crash has occured (THIS DOES NOT MEAN IT WAS INSIGHT, THIS IS JUST HERE FOR DEBUGGING PURPOSES)	
[01:29:18]: [workshop-2189004162 (Insight)]:		Title:	警告！	
[01:29:18]: [workshop-2189004162 (Insight)]:		Text:	[string "../mods/thinking/scripts/screens/caravanscr..."]:65: attempt to call field 'ScrollBox' (a nil value)
LUA ERROR stack traceback:
    ../mods/thinking/scripts/screens/caravanscreen.lua:65 in (field) _ctor (Lua) <8-95>
    scripts/class.lua:191 in (upvalue) CaravanScreen (Lua) <181-194>
    ../mods/thinking/modmain.lua:140 in (field) fn (Lua) <130-149>
    scripts/events.lua:46 in (method) HandleEvent (Lua) <42-49>
    scripts/input.lua:189 in (method) OnRawKey (Lua) <186-193>
    scripts/input.lua:765 in () ? (Lua) <764-766>
	
[01:29:18]: [workshop-2189004162 (Insight)]:		Additionaltext:	此错误可能是由于你启用的某个模组所致！
你启用了下列模组：
"商旅巡游录 | Caravan Cycle" "Too Many Items Plus" "Insight" 	
[01:29:18]: [workshop-2189004162 (Insight)]:	Checking if we can send a crash report.	
[01:29:18]: [workshop-2189004162 (Insight)]:	CanWeSendReport:	false	server did not find an authoritative opt-in	
[01:29:22]: Collecting garbage...
[01:29:23]: lua_gc took 0.43 seconds
[01:29:23]: ~ShardLuaProxy()
[01:29:23]: ~cEventLeaderboardProxy()
[01:29:23]: ~ItemServerLuaProxy()
[01:29:23]: ~InventoryLuaProxy()
[01:29:23]: ~NetworkLuaProxy()
[01:29:23]: ~SimLuaProxy()
[01:29:23]: [Workshop] CancelDownloads for all pending downloads
[01:29:23]: lua_close took 0.54 seconds
[01:29:23]: ReleaseAll
[01:29:23]: ReleaseAll Finished
[01:29:23]: cGame::StartPlaying
[01:29:23]: AppVersion::GetArchitecture() x64
[01:29:23]: LOADING LUA
[01:29:23]: DoLuaFile scripts/main.lua
[01:29:23]: DoLuaFile loading buffer scripts/main.lua
[01:29:23]: Translator:LoadPOFile - loading file: scripts/languages/chinese_s.po	
[01:29:25]:   taskgrouplist:	default	联机版	
[01:29:25]:   taskgrouplist:	classic	经典	
[01:29:25]:   taskgrouplist:	cave_default	地下	
[01:29:25]:   taskgrouplist:	lavaarena_taskset	熔炉	
[01:29:25]:   taskgrouplist:	quagmire_taskset	暴食	
[01:29:26]: Running main.lua
	
[01:29:26]: unloading prefabs for mod thinking (商旅巡游录 | Caravan Cycle)	
[01:29:26]: unloading prefabs for mod workshop-1365141672 (Too Many Items Plus)	
[01:29:26]: unloading prefabs for mod workshop-2189004162 (Insight)	
[01:29:26]: loaded modindex	
[01:29:26]: ModIndex: Beginning normal load sequence.
	
