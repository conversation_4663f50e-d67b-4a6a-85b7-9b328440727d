# 工匠大师和怪物磁铁词条修复说明

## 🔴 问题描述

**用户反馈：**
1. **工匠大师**：制作不消耗物品（完全免费制作）
2. **怪物磁铁**：没有效果，怪物行为没有变化

## 🔍 问题分析

### 1. 工匠大师问题分析

**原始错误实现：**
- 修改了 `RemoveIngredients` 方法
- 但这个方法可能不是在所有制作流程中都被调用
- 导致材料消耗计算出现问题，甚至完全不消耗

**根本原因：**
- 饥荒的制作系统中，材料需求是通过 `GetIngredients` 方法获取的
- `RemoveIngredients` 只是执行消耗，而不是计算需求
- 应该修改需求计算而不是消耗执行

### 2. 怪物磁铁问题分析

**原始错误实现：**
- 只是添加了一些标签
- 没有真正影响怪物的AI行为
- 怪物的追击逻辑没有被修改

**根本原因：**
- 饥荒的怪物AI是通过combat组件和brain系统控制的
- 仅仅添加标签不足以改变怪物的行为
- 需要主动干预怪物的目标选择和追击逻辑

## ✅ 修复方案

### 1. 工匠大师修复

**修复思路：**
- 修改 `GetIngredients` 方法而不是 `RemoveIngredients`
- 在获取材料需求时就减半，确保整个制作流程都使用减半的材料

**修复后的实现：**
```lua
master_crafter = function(player)
    if not player or not player.components or not player.components.builder then return end

    -- 通过修改builder的GetIngredients方法来实现材料减半
    local original_get_ingredients = player.components.builder.GetIngredients
    player.components.builder.GetIngredients = function(self, recname)
        local ingredients, discounted = original_get_ingredients(self, recname)
        if ingredients then
            -- 创建减半的材料表
            local reduced_ingredients = {}
            for _, ingredient in ipairs(ingredients) do
                local reduced_amount = math.max(1, math.ceil(ingredient.amount * 0.5))
                table.insert(reduced_ingredients, {type = ingredient.type, amount = reduced_amount})
            end
            return reduced_ingredients, discounted
        end
        return ingredients, discounted
    end

    return function()
        if player and player:IsValid() and player.components.builder then
            player.components.builder.GetIngredients = original_get_ingredients
        end
    end
end
```

**修复要点：**
- 修改 `GetIngredients` 而不是 `RemoveIngredients`
- 确保材料减半但最少消耗1个
- 保持原有的折扣机制

### 2. 怪物磁铁修复

**修复思路：**
- 主动干预怪物的combat组件
- 定期强制设置玩家为怪物的攻击目标
- 修改怪物的追击超时时间，让其持续追击

**修复后的实现：**
```lua
monster_magnet = function(player)
    if not player or not player.components then return end

    -- 添加标签让怪物优先攻击
    player:AddTag("monster_target")
    
    -- 定期强制让附近怪物攻击玩家
    local function ForceMonsterTarget()
        if not (player and player:IsValid()) then return end
        
        local x, y, z = player.Transform:GetWorldPosition()
        -- 查找附近的敌对生物
        local monsters = TheSim:FindEntities(x, y, z, 20, {"_combat"}, {"player", "wall", "structure"})
        
        for _, monster in ipairs(monsters) do
            if monster and monster:IsValid() and monster.components.combat then
                -- 检查是否是敌对生物
                if monster.components.combat.defaultdamage and monster.components.combat.defaultdamage > 0 
                   and not monster:HasTag("player") and not monster:HasTag("companion") then
                    -- 强制设置玩家为目标
                    monster.components.combat:SetTarget(player)
                    -- 重置放弃追击的时间，让怪物持续追击
                    if monster.components.combat.keeptargettimeout then
                        monster.components.combat.keeptargettimeout = 999999 -- 几乎永不放弃
                    end
                    -- 如果怪物有大脑，强制其进入攻击状态
                    if monster.brain then
                        monster.brain:ForceUpdate()
                    end
                end
            end
        end
    end

    -- 每2秒检查一次附近怪物
    local task = player:DoPeriodicTask(2, ForceMonsterTarget)
    ForceMonsterTarget() -- 立即执行一次
    
    return function()
        if task then
            task:Cancel()
            task = nil
        end
        if player and player:IsValid() then
            player:RemoveTag("monster_target")
        end
    end
end
```

**修复要点：**
- 定期扫描附近20格内的敌对生物
- 强制设置玩家为攻击目标
- 修改追击超时时间，让怪物几乎永不放弃追击
- 强制更新怪物大脑状态

## 🧪 测试方法

### 工匠大师测试
```lua
-- 1. 激活词条
c_findmutator("工匠")

-- 2. 测试简单制作
c_give("twigs", 4)
c_give("flint", 4)
-- 制作斧头，正常需要1树枝+1燧石，现在应该还是1+1（减半后向上取整）

-- 3. 测试复杂制作
c_give("log", 10)
c_give("rocks", 10)
c_give("goldnugget", 5)
-- 制作科学机器，正常需要4木头+4石头+1金子，现在应该需要2木头+2石头+1金子
```

### 怪物磁铁测试
```lua
-- 1. 激活词条
c_findmutator("怪物")

-- 2. 生成怪物测试
c_spawn("spider", 3)
c_spawn("hound", 2)

-- 3. 观察怪物行为
-- 怪物应该立即锁定玩家并持续追击
-- 即使玩家跑得很远，怪物也不会放弃追击
-- 每2秒会重新强制锁定玩家
```

## 📊 预期效果

| 词条 | 修复前 | 修复后 |
|------|--------|--------|
| 工匠大师 | 完全不消耗材料 | 材料消耗减半（最少1个） |
| 怪物磁铁 | 没有效果 | 怪物持续追击玩家，几乎永不放弃 |

## 🎯 技术要点

### 工匠大师技术要点
1. **正确的Hook点**：修改 `GetIngredients` 而不是 `RemoveIngredients`
2. **材料计算**：使用 `math.ceil(amount * 0.5)` 确保减半后向上取整
3. **最小值保证**：使用 `math.max(1, ...)` 确保至少消耗1个材料
4. **兼容性**：保持原有的折扣机制不变

### 怪物磁铁技术要点
1. **范围检测**：使用 `TheSim:FindEntities` 查找附近怪物
2. **目标强制**：使用 `combat:SetTarget` 强制设置攻击目标
3. **持续追击**：修改 `keeptargettimeout` 让怪物几乎永不放弃
4. **AI更新**：使用 `brain:ForceUpdate` 强制更新怪物AI状态
5. **定期检查**：每2秒重新扫描和设置，确保效果持续

## 💡 经验教训

1. **深入理解系统**：需要深入理解饥荒的制作系统和AI系统
2. **正确的Hook点**：找到正确的方法进行修改，而不是盲目修改
3. **测试验证**：修改后需要充分测试验证效果
4. **官方源码参考**：查阅官方源码了解正确的实现方式

现在这两个词条应该能正常工作了！
