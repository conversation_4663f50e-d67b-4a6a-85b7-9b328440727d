# 负重前行和天气词条修复说明

## 🔧 修复的问题

### 1. 负重前行词条无效果问题

**问题描述：**
- 玩家背包15个物品栏都满了，但移动速度没有变化
- 词条效果没有正常触发

**原因分析：**
- API方法是正确的：`inventory:NumItems()` 返回"被使用的格子数目(0-15)"
- 问题在于逻辑条件：使用了 `> 15` 而不是 `>= 15`
- 当背包满时，NumItems()返回15，条件应该是 `>= 15` 才能触发效果

**解决方案：**
- 保持使用 `inventory:NumItems()` API（这是正确的）
- 修改触发条件从 `> 15` 改为 `>= 15`
- 更新描述为"物品栏满时移动缓慢"更准确

**修复后的代码：**
```lua
-- 修复前（错误的条件）
if total_items > 15 then

-- 修复后（正确的条件）
if used_slots >= 15 then
```

### 2. 阴雨连绵词条不下雨问题

**问题描述：**
- 游戏里没有下雨，只是潮湿值上升
- 用户期望看到真正的下雨效果

**解决方案：**
- 移除了潮湿值的手动增加
- 添加了真正的天气控制代码
- 尝试多种方法启动降雨：
  - 设置 `TheWorld.state.precipitationrate = 1.0`
  - 设置 `TheWorld.state.israining = true`
  - 调用 `weather:StartPrecip()` 或 `weather:SetPrecipitationRate(1.0)`

**修复后的效果：**
- 游戏会真正下雨
- 描述改为"阴雨连绵：持续下雨"
- 移除了"潮湿值上升"的描述

### 3. 寒流来袭词条优化

**问题描述：**
- 用户希望确保玩家在火炉旁时体温不会下降
- 温度下降速度太快，需要调整为更合理的速率

**解决方案：**
- 添加了火炉检测逻辑
- 检查玩家周围4格内是否有火炉/篝火
- 检查玩家周围6格内是否有庇护所
- 只有在没有热源和庇护的情况下才降温
- 降温速率从每秒-5调整为每秒-1.25（原来的1/4）

**修复后的逻辑：**
```lua
-- 检查火炉/篝火
local nearby_heaters = TheSim:FindEntities(x, y, z, 4, {"campfire", "firepit", "dragonflyfurnace"})

-- 检查庇护所
local shelters = TheSim:FindEntities(x, y, z, 6, {"shelter"})

-- 只有在没有热源时才降温，且降温速率减少到1/4
if #nearby_heaters == 0 and #shelters == 0 then
    player.components.temperature:DoDelta(-1.25) -- 从-5改为-1.25
end
```

## 📋 技术细节

### 负重前行词条的逻辑修复

| 修复前 | 修复后 | 说明 |
|--------|--------|------|
| `if total_items > 15` | `if used_slots >= 15` | 条件修复：满格时应该触发 |
| "携带物品超过15个时" | "物品栏满时" | 描述更准确 |
| `NumStackedItems()` | `NumItems()` | 使用正确的API：检测格子数而非物品总数 |

### 天气系统的控制方法

尝试了多种天气控制方法：
1. **世界状态控制**：`TheWorld.state.precipitationrate`、`TheWorld.state.israining`
2. **Weather组件控制**：`weather:StartPrecip()`、`weather:SetPrecipitationRate()`
3. **全局效果**：确保只在主服务器执行一次，避免重复启动

### 寒流来袭的智能检测

- **火炉检测**：4格范围内的篝火、火坑、龙蝇熔炉
- **庇护检测**：6格范围内的庇护所建筑
- **降温速率**：从每秒-5温度调整为每秒-1.25温度（1/4速率）
- **合理性**：符合游戏逻辑，火炉旁应该暖和，降温不会太快

## 🧪 测试建议

### 负重前行测试
```lua
-- 1. 寻找词条
c_findmutator("负重")

-- 2. 准备测试物品（填满15个格子）
c_give("log", 1)
c_give("rocks", 1)
c_give("twigs", 1)
c_give("cutgrass", 1)
c_give("flint", 1)
-- ... 继续给不同物品直到15个格子都满

-- 3. 观察移动速度变化
-- 现在应该能正确检测到15个格子满并减速
```

### 阴雨连绵测试
```lua
-- 1. 寻找词条
c_findmutator("阴雨")

-- 2. 观察天气变化
-- 应该看到真正的下雨效果，而不只是潮湿值变化
```

### 寒流来袭测试
```lua
-- 1. 寻找词条
c_findmutator("寒流")

-- 2. 测试不同环境
-- 在空旷地区：温度应该下降
-- 在篝火旁：温度不应该下降
-- 在建筑内：温度不应该下降

-- 3. 生成测试环境
c_spawn("campfire")  -- 生成篝火测试
```

## ⚠️ 注意事项

1. **负重前行**：现在会正确计算堆叠物品的总数
2. **阴雨连绵**：作为全局效果，只在主服务器执行一次
3. **寒流来袭**：智能检测热源，更符合游戏逻辑
4. **调试信息**：所有词条都添加了详细的调试输出

## 📈 预期效果

修复后，这三个词条应该：
- ✅ **负重前行**：正确检测物品栏格子数，满格时减速
- ✅ **阴雨连绵**：游戏真正下雨，视觉效果明显
- ✅ **寒流来袭**：智能降温，火炉旁保持温暖

这些修复让词条效果更加真实和合理，提升了游戏体验。
