# 多人游戏词条修复说明

## 🔴 问题描述

### 1. 阴雨连绵多人触发问题
**问题**：多个玩家同时拥有阴雨连绵词条时，任何一个玩家移除词条都会停止全世界的降雨，影响其他仍持有该词条的玩家。

**原因**：使用了简单的"谁调用谁清理"机制，没有考虑多人同时持有的情况。

### 2. 玻璃大炮生命值同步问题
**问题**：直接修改 `currenthealth` 字段会绕过网络同步与内部校验，可能导致客户端和服务器数据不一致。

**原因**：没有使用正确的组件接口来修改生命值。

## ✅ 修复方案

### 1. 阴雨连绵：引用计数机制

**核心思路**：使用世界级引用计数，只要还有人持有词条，就保持下雨。

```lua
-- 引用计数 +1
GLOBAL.TheWorld._caravan_wet_weather_ref = (GLOBAL.TheWorld._caravan_wet_weather_ref or 0) + 1

if not GLOBAL.TheWorld._caravan_wet_weather_task then
    -- 首次启动：创建全局保活任务
    GLOBAL.TheWorld._caravan_wet_weather_active = true
    
    local function ForceRain()
        GLOBAL.TheWorld:PushEvent("ms_forceprecipitation", true)
        local weather = GLOBAL.TheWorld.components.weather
        if weather then
            if weather.StartPrecip then weather:StartPrecip() end
            if weather.SetPrecipitationRate then weather:SetPrecipitationRate(1.0) end
        end
    end

    GLOBAL.TheWorld:DoTaskInTime(0, ForceRain)
    GLOBAL.TheWorld._caravan_wet_weather_task = GLOBAL.TheWorld:DoPeriodicTask(30, ForceRain)
end

-- 清理函数：计数-1，只有归零时才真正停雨
return function()
    GLOBAL.TheWorld._caravan_wet_weather_ref = math.max(0, (GLOBAL.TheWorld._caravan_wet_weather_ref or 1) - 1)
    
    if GLOBAL.TheWorld._caravan_wet_weather_ref == 0 then
        -- 最后一个人移除时才停止降雨
        if GLOBAL.TheWorld._caravan_wet_weather_task then
            GLOBAL.TheWorld._caravan_wet_weather_task:Cancel()
            GLOBAL.TheWorld._caravan_wet_weather_task = nil
        end
        GLOBAL.TheWorld._caravan_wet_weather_active = nil
        GLOBAL.TheWorld:PushEvent("ms_forceprecipitation", false)
        local weather = GLOBAL.TheWorld.components.weather
        if weather and weather.StopPrecip then weather:StopPrecip() end
    end
end
```

**优势**：
- 支持多人同时持有词条
- 只有最后一个人移除时才停雨
- 避免了重复创建保活任务
- 引用计数确保资源正确管理

### 2. 玻璃大炮：正确的组件接口

**核心思路**：使用health组件的正确接口，确保网络同步和内部校验。

```lua
if player.components.health then
    local original_max_health = player.components.health.maxhealth
    
    -- ✅ 正确：使用组件接口设置最大生命值
    player.components.health:SetMaxHealth(original_max_health * 0.5)
    
    -- ✅ 正确：使用组件接口设置当前生命值
    player.components.health:SetCurrentHealth(
        math.min(player.components.health.currenthealth, player.components.health.maxhealth)
    )
    
    -- ✅ 正确：强制网络同步（不触发伤害/治疗浮字）
    player.components.health:DoDelta(0, true)
end

-- 清理时同样使用组件接口
return function()
    if player.components.health then
        player.components.health:SetMaxHealth(original_max_health)
        player.components.health:SetCurrentHealth(
            math.min(player.components.health.currenthealth, player.components.health.maxhealth)
        )
        player.components.health:DoDelta(0, true)
    end
end
```

**对比错误做法**：
```lua
-- ❌ 错误：直接修改字段
player.components.health.currenthealth = new_value

-- ✅ 正确：使用组件接口
player.components.health:SetCurrentHealth(new_value)
player.components.health:DoDelta(0, true) -- 强制同步
```

## 🧪 测试方法

### 阴雨连绵多人测试
```lua
-- 玩家A获得词条
c_findmutator("阴雨")
-- 观察：开始下雨，引用计数=1

-- 玩家B也获得词条
-- 观察：继续下雨，引用计数=2，但不会重复创建保活任务

-- 玩家A移除词条
c_rerolll()
-- 观察：仍在下雨，引用计数=1

-- 玩家B移除词条
-- 观察：停止下雨，引用计数=0，清理保活任务
```

### 玻璃大炮生命值测试
```lua
-- 测试生命值变化
c_findmutator("玻璃")
-- 观察：最大生命值减半，当前生命值相应调整

-- 检查网络同步
-- 在多人游戏中，其他玩家应该能看到正确的生命值显示

-- 测试移除效果
c_rerolll()
-- 观察：最大生命值恢复，当前生命值正确调整
```

## 📊 修复效果对比

| 词条 | 修复前问题 | 修复后效果 |
|------|------------|------------|
| 阴雨连绵 | 任何一人移除就停雨 | 最后一人移除才停雨 |
| 玻璃大炮 | 网络同步问题 | 正确的网络同步 |

## 🎯 技术要点

### 引用计数机制
1. **计数管理**：每次应用词条时 +1，移除时 -1
2. **资源共享**：多个玩家共享同一个世界保活任务
3. **安全清理**：只有计数归零时才清理资源
4. **防护机制**：使用 `math.max(0, ...)` 防止计数变负

### 组件接口使用
1. **SetMaxHealth()**：正确设置最大生命值
2. **SetCurrentHealth()**：正确设置当前生命值
3. **DoDelta(0, true)**：强制网络同步，不触发浮字
4. **避免直接修改**：不直接修改 `currenthealth` 等字段

## 💡 设计原则

### 多人游戏考虑
1. **共享资源**：全局效果使用引用计数管理
2. **状态同步**：确保所有客户端看到一致的状态
3. **公平性**：不因个别玩家的操作影响其他玩家
4. **资源管理**：避免资源泄漏和重复创建

### 组件系统最佳实践
1. **使用官方接口**：优先使用组件提供的方法
2. **网络同步**：确保客户端和服务器数据一致
3. **内部校验**：让组件自己处理数据验证
4. **事件触发**：正确触发相关事件和回调

## 🔧 扩展应用

这些修复原则可以应用到其他类似的词条：

### 全局效果词条
- 使用引用计数管理共享资源
- 避免重复创建全局任务
- 确保最后一个用户才清理资源

### 属性修改词条
- 使用组件接口而不是直接修改字段
- 调用 `DoDelta(0, true)` 强制同步
- 保存原始值用于恢复

现在这两个词条在多人游戏中应该能正确工作了！
