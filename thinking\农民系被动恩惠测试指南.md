# 农民系被动恩惠测试指南

## 新增职业概述

农民系是职业被动恩惠系统的第四个职业方向，专注于农业生产和采集强化，为喜欢建设和资源收集的玩家提供专门的强化路径。

## 农民系被动恩惠详细说明

### 1. 绿拇指 (farmer_growth)
- **效果**：玩家周围8格范围内植物生长速度加快100%
- **费用**：30恩惠
- **实现机制**：
  - 每10秒扫描一次周围8格范围内的植物
  - 对有growable组件的植物减少生长时间
  - 生长速度提升100%（即生长时间减半）
- **适用植物**：
  - 农场作物（胡萝卜、玉米、南瓜等）
  - 浆果丛、草丛、树枝丛
  - 树木（桦树、常青树等）
  - 其他有生长阶段的植物

### 2. 自动收获 (farmer_harvest)
- **效果**：玩家周围1格范围内成熟作物自动收获
- **费用**：55恩惠
- **实现机制**：
  - 每5秒扫描一次周围1格范围内的可采集物
  - 自动执行采集动作，获得物品
  - 物品优先放入背包，背包满时掉落在地
  - 触发采集事件，与丰收祝福联动
- **适用对象**：
  - 成熟的农场作物
  - 浆果丛、草丛、树枝丛
  - 花朵、蘑菇等可采集物
  - 任何有pickable组件的物品

### 3. 丰收祝福 (farmer_blessing)
- **效果**：采集时恢复饥饿+10、理智+5、生命+3
- **费用**：75恩惠
- **实现机制**：
  - 监听picksomething事件
  - 每次采集时恢复三维属性
  - 显示"丰收祝福!"提示
  - 与自动收获完美配合
- **恢复数值**：
  - 饥饿值：+10点
  - 理智值：+5点
  - 生命值：+3点

## 职业特色与定位

### 设计理念
- **生产力强化**：专注于提升农业和采集效率
- **自动化程度**：减少重复性劳动，解放双手
- **生存支援**：通过采集恢复三维，提升生存能力
- **建设导向**：适合喜欢建设基地的玩家

### 游戏风格
- **和平发展**：不依赖战斗，专注于资源积累
- **长期收益**：效果持续性强，适合长期游戏
- **基地建设**：与农场、花园建设完美配合
- **团队支援**：为团队提供稳定的资源供应

## 测试步骤

### 1. 解锁农民系被动恩惠
```
cboon unlock farmer_growth    # 解锁绿拇指（30恩惠）
cboon unlock farmer_harvest   # 解锁自动收获（55恩惠）
cboon unlock farmer_blessing  # 解锁丰收祝福（75恩惠）
```

### 2. 装备测试
```
cboon equip farmer_growth     # 装备绿拇指
cboon equip farmer_harvest    # 装备自动收获
```

### 3. 绿拇指测试
1. **准备测试环境**：
   - 种植一些农场作物（胡萝卜、玉米等）
   - 在附近放置浆果丛、草丛
   - 种植一些树苗

2. **测试步骤**：
   - 装备绿拇指被动恩惠
   - 站在植物附近（8格范围内）
   - 观察植物生长速度是否明显加快
   - 对比装备前后的生长时间

3. **预期结果**：
   - 植物生长速度提升100%
   - 原本需要3天成熟的作物，现在1.5天即可成熟
   - 效果范围为玩家周围8格

### 4. 自动收获测试
1. **准备测试环境**：
   - 等待作物成熟
   - 在附近放置成熟的浆果丛、草丛
   - 确保背包有空位

2. **测试步骤**：
   - 装备自动收获被动恩惠
   - 站在成熟作物附近（1格范围内）
   - 等待5秒，观察是否自动收获
   - 检查背包是否获得物品

3. **预期结果**：
   - 每5秒自动收获一次
   - 成熟作物自动被采集
   - 物品自动进入背包
   - 背包满时物品掉落在地

### 5. 丰收祝福测试
1. **准备测试环境**：
   - 确保角色三维不满
   - 准备一些可采集的物品

2. **测试步骤**：
   - 装备丰收祝福被动恩惠
   - 手动采集任何物品
   - 观察三维属性变化
   - 查看是否显示"丰收祝福!"提示

3. **预期结果**：
   - 每次采集恢复饥饿+10、理智+5、生命+3
   - 显示"丰收祝福!"提示
   - 与自动收获联动时效果叠加

### 6. 组合效果测试
1. **最佳组合**：
   - 绿拇指 + 自动收获：快速生长 + 自动采集
   - 自动收获 + 丰收祝福：自动采集 + 三维恢复

2. **测试场景**：
   - 建立一个小型农场
   - 装备两个农民系被动恩惠
   - 观察整体效果和游戏体验

## UI界面验证

### 1. 职业分类显示
使用 `cui` 命令打开界面：
- 应该显示"农民系"分类
- 包含3种农民系被动恩惠
- 显示正确的费用和状态

### 2. 命令系统
```
cboon list                    # 应该显示农民系被动恩惠
cboon unlock farmer_growth    # 解锁测试
cboon equip farmer_growth     # 装备测试
cboon unequip farmer_growth   # 卸下测试
```

## 平衡性验证

### 费用合理性
- **绿拇指**：30恩惠（基础效果，费用较低）
- **自动收获**：55恩惠（便利性强，费用中等）
- **丰收祝福**：75恩惠（综合强化，费用较高）

### 效果强度
- **生长加速**：100%提升明显但不过强
- **自动收获**：1格范围较小，5秒间隔合理，避免过于强大
- **三维恢复**：数值适中，不会破坏游戏平衡

### 职业定位
- 与战士、法师、召唤师形成互补
- 专注于非战斗玩法
- 适合和平发展的玩家

## 常见问题

### Q: 绿拇指对哪些植物有效？
A: 对所有有growable组件的植物有效，包括农场作物、浆果丛、草丛、树木等

### Q: 自动收获会不会太强？
A: 1格范围和5秒间隔的设计确保了平衡性，需要玩家站在作物旁边，不会过于强大

### Q: 丰收祝福的恢复量是否合适？
A: 饥饿+10、理智+5、生命+3的设计既有用又不会破坏游戏平衡

### Q: 农民系可以和其他职业混搭吗？
A: 可以，最多装备2个被动恩惠，支持跨职业混搭

## 开发价值

农民系的加入使职业被动恩惠系统更加完整：
1. **四大职业方向**：战士、法师、召唤师、农民
2. **多样化玩法**：战斗、技能、策略、建设
3. **玩家选择**：满足不同类型玩家的需求
4. **系统完整性**：覆盖游戏的主要玩法方向

农民系为喜欢和平发展、基地建设的玩家提供了专门的强化路径，丰富了游戏的深度和可玩性！
