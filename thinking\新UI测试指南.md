# 新被动恩惠UI测试指南

## 功能概述
新的被动恩惠UI采用技能树样式设计，支持：
1. **鼠标悬停显示描述** - 鼠标移到被动恩惠按钮上显示详细信息
2. **点击进行加点/洗点** - 直接点击按钮进行解锁、装备、卸下操作
3. **职业分类布局** - 四个职业分别显示在不同区域
4. **状态可视化** - 不同颜色表示不同状态

## UI布局说明

### 整体布局
- **顶部**: 显示Favor代币余额和装备情况
- **中央**: 四个职业区域，每个区域包含该职业的被动恩惠
- **底部**: 洗点重置按钮

### 职业区域分布
```
战士系 (红色)          法师系 (蓝色)
  ↑                      ↑
左上角                  右上角

召唤师系 (紫色)        农民系 (绿色)
  ↑                      ↑
左下角                  右下角
```

### 按钮状态颜色
- **深灰色**: 未解锁状态
- **黄色**: 已解锁但未装备
- **绿色**: 已装备状态

## 测试步骤

### 1. 打开新UI
```
cui
```
或按V键，然后切换到"被动恩惠"标签页

### 2. 测试鼠标悬停
1. 将鼠标移动到任意被动恩惠按钮上
2. 应该显示tooltip，包含：
   - 被动恩惠名称
   - 详细描述
   - 费用信息
   - 当前状态
   - 操作提示

### 3. 测试解锁功能
1. 确保有足够的Favor代币（使用`cfavor 100`添加）
2. 点击未解锁的被动恩惠按钮
3. 应该：
   - 扣除相应费用
   - 按钮变为黄色（已解锁）
   - 显示解锁成功消息

### 4. 测试装备功能
1. 点击已解锁但未装备的被动恩惠
2. 应该：
   - 按钮变为绿色（已装备）
   - 显示装备成功消息
   - 应用相应效果

### 5. 测试卸下功能
1. 点击已装备的被动恩惠
2. 应该：
   - 按钮变为黄色（已解锁但未装备）
   - 显示卸下成功消息
   - 移除相应效果

### 6. 测试装备限制
1. 尝试装备超过2个被动恩惠
2. 应该显示"装备槽已满"的提示

### 7. 测试洗点功能
1. 装备一些被动恩惠
2. 点击"洗点重置"按钮
3. 应该：
   - 计算洗点费用（装备数量 × 10）
   - 卸下所有装备的被动恩惠
   - 重置所有解锁状态
   - 返还解锁费用，扣除洗点费用

## 预期效果

### 视觉效果
- 四个职业区域清晰分布
- 按钮颜色正确反映状态
- 鼠标悬停时显示详细tooltip

### 交互效果
- 点击响应迅速
- 状态变化立即反映在UI上
- 操作反馈清晰

### 功能效果
- 解锁/装备/卸下功能正常
- 洗点功能正确计算费用
- 所有操作都有相应的聊天提示

## 常见问题排查

### UI不显示
- 检查是否正确切换到"被动恩惠"标签页
- 确认玩家组件正常加载

### 鼠标悬停无效果
- 检查SetHoverText是否正确调用
- 确认tooltip文本格式正确

### 按钮点击无响应
- 检查OnBoonButtonClick方法是否正确绑定
- 确认玩家组件状态正常

### 颜色显示异常
- 检查按钮状态判断逻辑
- 确认颜色设置代码正确

## 技术实现要点

### 鼠标悬停实现
使用`button:SetHoverText(tooltip_text)`方法实现鼠标悬停显示描述

### 按钮状态管理
通过检查`unlocked_boons`和`equipped_boons`数组来确定按钮状态

### 颜色系统
- 使用`SetTint`方法设置按钮背景色
- 使用`SetColour`方法设置文字颜色

### 布局系统
- 使用固定坐标定位各职业区域
- 按费用排序显示被动恩惠

这个新UI系统提供了更直观、更便捷的被动恩惠管理体验，玩家可以通过简单的鼠标操作完成所有加点和洗点操作。
