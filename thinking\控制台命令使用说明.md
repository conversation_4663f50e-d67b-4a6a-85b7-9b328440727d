# 控制台命令使用说明

## 🎯 问题解决

之前的 `c_rerolll()` 命令无法在控制台中找到的问题已经修复！现在可以正常使用了。

## 🔧 如何使用

### 1. 打开控制台
- **按 ` 键**（键盘左上角，数字1左边的键）
- 控制台会在屏幕底部出现

### 2. 可用的控制台命令

#### 基础测试命令
```lua
c_rerolll()              -- 重掷词条（无限制）
c_mutators()             -- 查看当前词条
c_findmutator("词条名")   -- 自动寻找特定词条
```

#### 使用示例
```lua
-- 查看当前有什么词条
c_mutators()

-- 重掷获得新词条
c_rerolll()

-- 自动寻找包含"丰收"的词条
c_findmutator("丰收")

-- 自动寻找包含"疾风"的词条
c_findmutator("疾风")

-- 自动寻找包含"玻璃"的词条
c_findmutator("玻璃")
```

## 🧪 快速测试流程

### 第一步：准备测试环境
```lua
c_godmode()              -- 开启上帝模式
c_give("backpack", 1)    -- 获得背包
c_give("axe", 5)         -- 获得斧头
c_give("pickaxe", 5)     -- 获得镐子
```

### 第二步：开始测试词条
```lua
c_mutators()             -- 查看当前词条
c_findmutator("目标词条") -- 寻找特定词条
-- 进行相应的测试
c_rerolll()              -- 重掷获得新词条
```

## 📋 20个词条快速查找

### 正向词条
```lua
c_findmutator("丰收")     -- 丰收之日
c_findmutator("疾风")     -- 疾风步伐
c_findmutator("铁胃")     -- 铁胃
c_findmutator("澄澈")     -- 澄澈心智
c_findmutator("幸运")     -- 幸运打击
c_findmutator("工匠")     -- 工匠大师
c_findmutator("夜视")     -- 夜视
c_findmutator("兽语")     -- 兽语者
```

### 负向词条
```lua
c_findmutator("脆弱")     -- 脆弱工具
c_findmutator("不眠")     -- 不眠之夜
c_findmutator("笨拙")     -- 笨拙之手
c_findmutator("负重")     -- 负重前行
c_findmutator("怪物")     -- 怪物磁铁
c_findmutator("阴雨")     -- 阴雨连绵
c_findmutator("寒流")     -- 寒流来袭
c_findmutator("腐败")     -- 腐败加速
```

### 中性/事件词条
```lua
c_findmutator("玻璃")     -- 玻璃大炮
c_findmutator("夜猫")     -- 夜猫子
c_findmutator("狂战")     -- 狂战士
c_findmutator("商人")     -- 商人造访
```

## 🎮 实际测试示例

### 测试丰收之日
```lua
-- 1. 寻找词条
c_findmutator("丰收")

-- 2. 准备测试
c_spawn("sapling")       -- 生成小树苗

-- 3. 进行测试
-- 手动采集小树苗，观察是否获得双倍产出

-- 4. 测试完成后重掷
c_rerolll()
```

### 测试疾风步伐
```lua
-- 1. 寻找词条
c_findmutator("疾风")

-- 2. 测试移动速度
-- 观察角色移动是否明显变快

-- 3. 对比测试
c_rerolll()              -- 重掷移除词条
-- 再次观察移动速度，对比差异
```

### 测试玻璃大炮
```lua
-- 1. 寻找词条
c_findmutator("玻璃")

-- 2. 检查属性
-- 观察生命值是否减半，攻击力是否翻倍

-- 3. 战斗测试
c_spawn("spider")        -- 生成蜘蛛
-- 攻击蜘蛛，观察伤害数值
```

## ⚠️ 注意事项

### 使用提醒
1. **仅用于测试：** 这些命令专门用于测试词条效果
2. **无权限限制：** 任何玩家都可以使用
3. **建议环境：** 推荐在单人模式或测试服务器使用

### 常见问题
1. **控制台打不开？** 
   - 确保按的是 ` 键（不是单引号 '）
   - 有些键盘布局可能不同，尝试其他键

2. **命令不存在？**
   - 确保mod已正确加载
   - 重启游戏重新加载mod

3. **词条没有效果？**
   - 使用 `cstatus` 命令检查系统状态
   - 确保在主服务器上（单人游戏自动是主服务器）

## 🎯 测试目标

使用这些命令，你可以：
- ✅ 快速切换到任意词条
- ✅ 系统性测试所有20个词条
- ✅ 验证每个词条的效果
- ✅ 发现并报告问题
- ✅ 确保mod的稳定性

## 📈 下一步

1. **开始测试：** 使用上述命令开始系统性测试
2. **记录结果：** 详细记录每个词条的表现
3. **报告问题：** 如发现问题请详细描述
4. **完善系统：** 根据测试结果进行改进

现在你可以轻松地测试所有20个词条了！🎉
