# 被动恩惠系统测试指南

## 功能概述
被动恩惠系统已实现以下功能：
1. **6种被动恩惠**：移速、耐久、理智、饥饿、战斗、采集
2. **解锁系统**：使用Favor代币解锁被动恩惠
3. **装备系统**：最多同时装备2个被动恩惠
4. **效果应用**：自动应用和移除被动效果

## 被动恩惠列表

### 1. 轻装快行 (swift_movement)
- **效果**：移动速度提升15%
- **费用**：20恩惠
- **实现**：修改玩家locomotor组件的速度倍数

### 2. 匠心耐久 (durable_tools)
- **效果**：工具耐久消耗减少25%
- **费用**：25恩惠
- **实现**：监听working事件，有概率不消耗耐久

### 3. 夜行心定 (night_calm)
- **效果**：夜晚理智损失减少50%
- **费用**：30恩惠
- **实现**：监听sanitydelta事件，夜晚时减少理智损失

### 4. 饥不择食 (efficient_eating)
- **效果**：食物饱食效果提升20%
- **费用**：35恩惠
- **实现**：监听oneat事件，增加饥饿值恢复

### 5. 战斗专注 (combat_focus)
- **效果**：攻击时理智不下降
- **费用**：40恩惠
- **实现**：监听onattackother事件，攻击后恢复理智

### 6. 资源专家 (resource_efficiency)
- **效果**：采集时有15%概率额外获得1个物品
- **费用**：45恩惠
- **实现**：监听picksomething事件，概率生成额外物品

## 测试步骤

### 1. 获取恩惠代币
首先需要获得足够的恩惠代币：
- 完成合约任务获得恩惠
- 使用 `cboons` 查看当前恩惠余额

### 2. 查看可用被动恩惠
使用命令：`cboon list`
- 应该显示所有6种被动恩惠
- 显示每种被动恩惠的状态（未解锁/已解锁/已装备）
- 显示解锁所需的恩惠费用

### 3. 解锁被动恩惠
使用命令：`cboon unlock <被动ID>`
例如：
- `cboon unlock swift_movement` - 解锁轻装快行
- `cboon unlock durable_tools` - 解锁匠心耐久

### 4. 装备被动恩惠
使用命令：`cboon equip <被动ID>`
例如：
- `cboon equip swift_movement` - 装备轻装快行
- `cboon equip durable_tools` - 装备匠心耐久

### 5. 测试被动效果

#### 测试移速加成
1. 装备 `swift_movement`
2. 观察移动速度是否明显提升
3. 卸下被动恩惠，速度应该恢复正常

#### 测试工具耐久
1. 装备 `durable_tools`
2. 使用工具进行工作（砍树、挖掘等）
3. 观察工具耐久消耗是否减少

#### 测试夜晚理智
1. 装备 `night_calm`
2. 等待夜晚到来
3. 观察理智下降速度是否减缓

#### 测试食物效果
1. 装备 `efficient_eating`
2. 食用任何食物
3. 观察饥饿值恢复是否增加

#### 测试战斗理智
1. 装备 `combat_focus`
2. 攻击任何生物
3. 观察理智是否不下降或快速恢复

#### 测试采集加成
1. 装备 `resource_efficiency`
2. 采集浆果、花朵等资源
3. 观察是否偶尔获得额外物品

### 6. 测试装备限制
- 尝试装备超过2个被动恩惠
- 应该提示装备槽已满
- 需要先卸下一个才能装备新的

### 7. 卸下被动恩惠
使用命令：`cboon unequip <被动ID>`
- 效果应该立即消失
- 装备槽释放，可以装备其他被动恩惠

## UI界面测试

### 1. 打开UI界面
使用 `cui` 命令或按V键打开界面

### 2. 查看被动恩惠标签页
- 应该显示当前恩惠余额
- 显示已解锁和已装备的数量
- 列出所有可用的被动恩惠及其状态
- 显示使用说明和被动恩惠ID

## 存档测试

### 1. 保存和加载
1. 装备一些被动恩惠
2. 保存游戏并退出
3. 重新加载存档
4. 检查被动恩惠是否正确恢复
5. 检查效果是否正常工作

### 2. 玩家重生测试
1. 装备被动恩惠
2. 让角色死亡并重生
3. 检查被动恩惠是否重新应用

## 多人游戏测试

### 1. 独立性测试
- 每个玩家的被动恩惠应该独立
- 一个玩家的装备不影响其他玩家

### 2. 新玩家加入
- 新加入的玩家应该能正常使用被动恩惠系统
- 不会受到其他玩家状态影响

## 预期结果

### 成功标准
1. ✅ 所有6种被动恩惠都能正确解锁
2. ✅ 装备系统工作正常（最多2个）
3. ✅ 所有被动效果都能正确应用
4. ✅ 卸下被动恩惠后效果消失
5. ✅ 存档加载后状态正确恢复
6. ✅ UI界面正确显示所有信息
7. ✅ 命令系统响应正确

### 常见问题

#### Q: 被动恩惠效果不生效？
A: 检查是否正确装备，使用 `cboons` 查看状态

#### Q: 无法解锁被动恩惠？
A: 检查恩惠余额是否足够，使用 `cboons` 查看

#### Q: 装备失败？
A: 检查是否已解锁，装备槽是否已满

#### Q: 重生后效果消失？
A: 系统会自动重新应用，稍等片刻

## 下一步开发
1. 更多被动恩惠类型
2. 被动恩惠升级系统
3. 被动恩惠组合效果
4. 特殊条件解锁的被动恩惠
