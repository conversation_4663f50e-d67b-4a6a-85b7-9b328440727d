# 合约系统测试指南

## 功能概述
合约系统已实现以下功能：
1. **击杀合约**：击败指定数量的生物
2. **交付合约**：收集并交付指定物品
3. **建设合约**：建造指定结构
4. **奖励系统**：完成合约获得恩惠(Favor)和阵营声望

## 测试步骤

### 1. 启动游戏并加载mod
- 确保mod已启用
- 创建新世界或加载现有存档

### 2. 查看当前合约
使用命令：`ccontracts`
- 应该显示3个随机生成的合约
- 每个合约显示类型、目标、进度和奖励

### 3. 测试击杀合约
如果有击杀合约（如击杀蜘蛛、触手等）：
1. 找到对应的生物
2. 击杀它们
3. 观察是否有进度广播
4. 使用 `ccontracts` 查看进度更新

### 4. 测试建设合约
如果有建设合约（如建造猪屋、路灯等）：
1. 收集所需材料
2. 建造对应结构
3. 观察是否有进度广播
4. 使用 `ccontracts` 查看进度更新

### 5. 测试交付合约
如果有交付合约（如交付金块、木板等）：
1. 收集所需物品
2. 使用命令：`cdeliver 合约编号`（如 `cdeliver 1`）
3. 观察物品是否被移除
4. 查看进度更新

### 6. 测试合约完成
当合约进度达到目标时：
1. 应该看到完成广播
2. 所有玩家获得恩惠奖励
3. 获得对应阵营声望
4. 自动生成新合约替换已完成的

### 7. 查看奖励
- 使用 `cboons` 查看恩惠余额
- 使用 `crep` 查看阵营声望
- 使用 `cui` 打开UI界面查看详细信息

## 预期结果

### 合约类型示例
- **击杀合约**：击杀蜘蛛10只，奖励5恩惠+2猪人声望
- **交付合约**：交付金块10个，奖励8恩惠+4猪人声望  
- **建设合约**：建造猪屋2个，奖励15恩惠+8猪人声望

### 系统特性
1. **全服共享**：所有玩家共同完成合约
2. **自动刷新**：完成的合约自动被新合约替换
3. **持久化**：合约进度在存档中保存
4. **多人协作**：任何玩家的行为都会推进合约进度

## 调试命令
- `cstatus`：查看系统状态
- `creapply`：重新应用词条效果
- `cui`：打开UI界面

## 常见问题

### Q: 合约没有生成？
A: 检查世界组件是否正确初始化，使用 `cstatus` 查看状态

### Q: 击杀没有计数？
A: 确保击杀的是合约指定的生物类型

### Q: 交付命令无效？
A: 检查命令格式：`cdeliver 合约编号`，确保背包中有足够物品

### Q: 建造没有计数？
A: 确保建造的是合约指定的结构类型

## 下一步开发
1. 更多合约类型（采集、探索等）
2. 合约难度分级
3. 限时合约
4. 合约奖励多样化
