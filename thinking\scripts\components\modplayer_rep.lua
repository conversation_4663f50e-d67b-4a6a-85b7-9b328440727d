-- Component files don't need GLOBAL declaration in DST

local ModPlayerRep = Class(function(self, inst)
    self.inst = inst
    self.reps = { pig = 0, cat = 0, bunny = 0 }
end)

function ModPlayerRep:AddReputation(faction, amount)
    if not self.reps[faction] then
        self.reps[faction] = 0
    end

    self.reps[faction] = self.reps[faction] + amount
    print("[商旅巡游录] 玩家", self.inst.userid or "unknown", "获得", faction, "声望", amount, "，总计:", self.reps[faction])

    if self.inst.components.talker then
        self.inst.components.talker:Say(string.format("%s声望 +%d！(总计: %d)", faction, amount, self.reps[faction]))
    end
end

function ModPlayerRep:CmdShowRep()
    local r = self.reps
    local msg = string.format("声望 pig:%d cat:%d bunny:%d", r.pig or 0, r.cat or 0, r.bunny or 0)
    if self.inst.components.talker then
        self.inst.components.talker:Say(msg)
    end
end

function ModPlayerRep:OnSave() return { reps = self.reps } end
function ModPlayerRep:OnLoad(data) if data and data.reps then self.reps = data.reps end end

return ModPlayerRep
