# 日期变化事件修复说明

## 🔴 问题描述

**用户反馈：**
- 游戏内到了下一天，词条没有自动转换
- 词条系统没有响应日期变化

## 🔍 问题分析

### 错误的事件名称
**修复前使用的事件：** `"ms_nextcycle"`
**正确的事件名称：** `"cycleschanged"`

### 官方源码分析
通过查阅饥荒官方源码，发现有两个相关事件：

| 事件名称 | 用途 | 说明 |
|----------|------|------|
| `"cycleschanged"` | 天数变化事件 | ✅ 正确的日期变化事件 |
| `"ms_nextcycle"` | 下一个周期事件 | ❌ 不是日期变化事件 |

从官方源码 `widgets/methods/worldresettimer.lua` 可以看到有 `_oncycleschanged` 方法，这证实了 `"cycleschanged"` 是正确的日期变化事件。

## ✅ 修复方案

### 1. 修复世界组件的事件监听

**文件：** `scripts/components/modworld_global.lua`

```lua
-- 修复前（错误）
inst:ListenForEvent("ms_nextcycle", function() self:OnNewDay() end, TheWorld)

-- 修复后（正确）
inst:ListenForEvent("cycleschanged", function() self:OnNewDay() end, TheWorld)
```

### 2. 修复玩家重投组件的事件监听

**文件：** `scripts/components/modplayer_reroll.lua`

```lua
-- 修复前（错误）
if TheWorld then
    inst:ListenForEvent("ms_nextcycle", function() self:OnNewDay() end, TheWorld)
else
    inst:DoTaskInTime(0, function()
        if TheWorld then
            inst:ListenForEvent("ms_nextcycle", function() self:OnNewDay() end, TheWorld)
        end
    end)
end

-- 修复后（正确）
if TheWorld then
    inst:ListenForEvent("cycleschanged", function() self:OnNewDay() end, TheWorld)
else
    inst:DoTaskInTime(0, function()
        if TheWorld then
            inst:ListenForEvent("cycleschanged", function() self:OnNewDay() end, TheWorld)
        end
    end)
end
```

### 3. 更新设计文档

**文件：** `DESIGN.md`

```markdown
-- 修复前
- 触发：ListenForEvent("ms_nextcycle")（官方日更事件）

-- 修复后
- 触发：ListenForEvent("cycleschanged")（官方日更事件）
```

## 📚 技术说明

### 事件系统理解
- 饥荒使用事件驱动架构
- 不同的游戏状态变化对应不同的事件
- 必须使用正确的事件名称才能正常监听

### 日期变化机制
- `"cycleschanged"` 事件在游戏天数发生变化时触发
- 这个事件会传递给所有监听者
- 是实现每日重置功能的标准方法

### 调试方法
可以通过以下方式验证事件是否正确触发：

```lua
-- 在组件初始化时添加调试监听
inst:ListenForEvent("cycleschanged", function()
    print("[调试] cycleschanged 事件触发，当前天数:", TheWorld.state.cycles)
end, TheWorld)
```

## 🧪 测试方法

### 验证修复效果
1. **加载游戏并等待到下一天**
2. **观察控制台输出**：
   ```
   [商旅巡游录] New day detected: [新天数]
   [商旅巡游录] Generated new mutators for day [新天数]
   ```
3. **检查词条是否更新**：
   ```lua
   cmutators  -- 查看当前词条
   ```
4. **验证重投次数重置**：
   ```lua
   creroll    -- 应该可以重新使用
   ```

### 测试场景
- **正常游戏流程**：等待自然的日夜循环
- **时间跳跃测试**：使用 `c_skip()` 跳过时间
- **存档加载测试**：保存后重新加载游戏

## 🎯 预期效果

修复后的系统应该：
- ✅ **自动更新词条**：每天自动生成新的词条
- ✅ **重投次数重置**：每天重置玩家的重投次数
- ✅ **持久化正常**：存档加载后功能正常
- ✅ **多人同步**：多人游戏中所有玩家同步更新

## 📋 相关文件修改列表

| 文件 | 修改内容 | 状态 |
|------|----------|------|
| `scripts/components/modworld_global.lua` | 事件名称从 ms_nextcycle 改为 cycleschanged | ✅ 已修复 |
| `scripts/components/modplayer_reroll.lua` | 事件名称从 ms_nextcycle 改为 cycleschanged | ✅ 已修复 |
| `DESIGN.md` | 更新设计文档中的事件名称 | ⏳ 待更新 |

## 🔧 故障排除

如果修复后仍然有问题：

1. **检查控制台输出**：查看是否有事件监听相关的错误
2. **验证TheWorld状态**：确保TheWorld已正确初始化
3. **测试事件触发**：添加调试代码验证事件是否触发
4. **检查组件挂载**：确保组件正确挂载到世界和玩家上

## 💡 经验教训

1. **查阅官方源码**：遇到事件问题时应该查阅官方源码确认正确的事件名称
2. **测试验证**：修改事件监听后应该充分测试验证
3. **文档同步**：修改代码后应该同步更新相关文档
4. **调试友好**：添加适当的调试输出帮助排查问题

现在词条系统应该能正确响应日期变化，每天自动更新词条了！
