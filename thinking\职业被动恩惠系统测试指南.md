# 职业被动恩惠系统测试指南

## 功能概述
全新的职业被动恩惠系统已实现，包含三大职业方向：
- **战士系**：移速、攻击力、连击系统
- **法师系**：毒伤、蓄力攻击、闪现术
- **召唤师系**：召唤蜘蛛、猪人、巨兽

## 被动恩惠详细说明

### 战士系 (Warrior)

#### 1. 战士疾行 (warrior_speed)
- **效果**：移动速度提升20%
- **费用**：25恩惠
- **测试方法**：装备后观察移动速度明显提升

#### 2. 战士之力 (warrior_damage)
- **效果**：攻击力提升30%
- **费用**：35恩惠
- **测试方法**：攻击任何生物，观察伤害数值增加

#### 3. 连击专精 (warrior_combo)
- **效果**：连续攻击同一目标时伤害递增(最多5层)
- **费用**：50恩惠
- **测试方法**：
  1. 连续攻击同一个敌人
  2. 观察"连击 x2!"、"连击 x3!"等提示
  3. 每层增加20%伤害，最多5层
  4. 3秒不攻击同一目标则重置

### 法师系 (Mage)

#### 1. 毒素掌控 (mage_poison)
- **效果**：攻击附带毒伤，每秒造成10点伤害，持续5秒
- **费用**：40恩惠
- **测试方法**：
  1. 攻击任何敌人
  2. 观察"中毒了!"提示
  3. 敌人每秒受到10点毒伤，持续5秒
  4. 有毒伤音效

#### 2. 蓄力打击 (mage_charge)
- **效果**：长按攻击键蓄力，最多增加200%伤害
- **费用**：60恩惠
- **测试方法**：
  1. 长按攻击键不放
  2. 蓄力时间越长伤害越高
  3. 每秒增加50%伤害，最多300%
  4. 观察"蓄力打击! x2.5"等提示

#### 3. 闪现术 (mage_teleport)
- **效果**：双击移动键瞬移8格距离(冷却30秒)
- **费用**：80恩惠
- **测试方法**：
  1. 快速双击移动键(WASD)
  2. 角色向面朝方向传送8格
  3. 观察"闪现!"提示和传送音效
  4. 30秒冷却时间

### 召唤师系 (Summoner)

#### 1. 蜘蛛召唤 (summon_spider)
- **效果**：召唤友好蜘蛛协助战斗(最多2只)
- **费用**：45恩惠
- **测试方法**：
  1. 装备后自动召唤蜘蛛战士
  2. 最多同时存在2只
  3. 蜘蛛会跟随玩家并攻击敌人
  4. 持续120秒后消失

#### 2. 猪人护卫 (summon_pig)
- **效果**：召唤猪人护卫跟随(最多1只)
- **费用**：70恩惠
- **测试方法**：
  1. 装备后自动召唤猪人
  2. 最多同时存在1只
  3. 猪人会跟随玩家并协助战斗
  4. 持续180秒后消失

#### 3. 巨兽召唤 (summon_boss)
- **效果**：召唤小型巨鹿协助战斗(最多1只)
- **费用**：120恩惠
- **测试方法**：
  1. 装备后自动召唤缩小版巨鹿
  2. 最多同时存在1只
  3. 巨鹿体型为原来的50%
  4. 持续60秒后消失

## 测试步骤

### 1. 获取恩惠代币
- 完成合约任务获得恩惠
- 新的被动恩惠费用较高，需要完成多个合约

### 2. 查看被动恩惠列表
使用命令：`cboon list`
- 应该显示按职业分类的9种被动恩惠
- 显示每种被动恩惠的状态和费用

### 3. 解锁被动恩惠
使用命令：`cboon unlock <被动ID>`
例如：
- `cboon unlock warrior_speed` - 解锁战士疾行
- `cboon unlock mage_poison` - 解锁毒素掌控
- `cboon unlock summon_spider` - 解锁蜘蛛召唤

### 4. 装备被动恩惠
使用命令：`cboon equip <被动ID>`
- 最多同时装备2个被动恩惠
- 可以混搭不同职业的被动恩惠

### 5. 测试各职业效果

#### 战士系测试
1. **移速测试**：装备warrior_speed，观察移动速度
2. **攻击力测试**：装备warrior_damage，攻击敌人观察伤害
3. **连击测试**：装备warrior_combo，连续攻击同一敌人

#### 法师系测试
1. **毒伤测试**：装备mage_poison，攻击敌人观察持续伤害
2. **蓄力测试**：装备mage_charge，长按攻击键测试
3. **传送测试**：装备mage_teleport，双击移动键测试

#### 召唤师系测试
1. **蜘蛛召唤**：装备summon_spider，观察召唤的蜘蛛
2. **猪人召唤**：装备summon_pig，观察召唤的猪人
3. **巨兽召唤**：装备summon_boss，观察召唤的小巨鹿

### 6. UI界面测试
使用 `cui` 命令打开界面：
- 查看被动恩惠标签页
- 应该显示按职业分类的被动恩惠
- 显示装备状态和使用说明

## 预期结果

### 成功标准
1. ✅ 所有9种被动恩惠都能正确解锁和装备
2. ✅ 战士系效果正确工作（移速、攻击力、连击）
3. ✅ 法师系效果正确工作（毒伤、蓄力、传送）
4. ✅ 召唤师系效果正确工作（召唤各种生物）
5. ✅ 装备限制正确执行（最多2个）
6. ✅ UI界面正确显示职业分类
7. ✅ 存档加载后状态正确恢复

### 平衡性验证
- **费用递增**：战士系25-50恩惠，法师系40-80恩惠，召唤师系45-120恩惠
- **效果强度**：高费用被动恩惠应该有明显更强的效果
- **职业特色**：每个职业都有独特的游戏体验

## 常见问题

### Q: 连击系统不工作？
A: 确保连续攻击同一个目标，3秒内不攻击会重置

### Q: 蓄力攻击没效果？
A: 需要长按攻击键，不是点击，蓄力时间越长伤害越高

### Q: 闪现术无法使用？
A: 需要快速双击移动键(WASD)，注意30秒冷却时间

### Q: 召唤物不攻击敌人？
A: 召唤物会自动跟随并攻击，如果不工作可能是AI问题

### Q: 毒伤看不到效果？
A: 毒伤是持续伤害，每秒10点，观察敌人血量持续下降

## 下一步开发
1. 更多职业分支和被动恩惠
2. 被动恩惠升级系统
3. 职业专精奖励
4. 被动恩惠组合效果
5. 特殊解锁条件的传说被动恩惠
